// Firebase Configuration and Authentication
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { 
    getAuth, 
    createUserWithEmailAndPassword, 
    signInWithEmailAndPassword, 
    signOut, 
    onAuthStateChanged,
    updateProfile
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration - these values will be loaded from the server
let firebaseConfig = {};

// Firebase app and auth instances
let app;
let auth;

// Initialize Firebase
async function initializeFirebase() {
    try {
        // Fetch Firebase config from server
        const response = await fetch('/api/firebase-config');
        if (!response.ok) {
            throw new Error('Failed to fetch Firebase configuration');
        }
        
        firebaseConfig = await response.json();
        
        // Initialize Firebase
        app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        
        console.log('Firebase initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        return false;
    }
}

// Authentication functions
export const FirebaseAuth = {
    // Initialize Firebase
    async init() {
        return await initializeFirebase();
    },

    // Get current user
    getCurrentUser() {
        return auth?.currentUser || null;
    },

    // Sign up with email and password
    async signUp(email, password, username) {
        try {
            // Validate inputs
            if (!email || !password || !username) {
                return {
                    success: false,
                    error: 'All fields are required'
                };
            }

            if (!email.includes('@') || !email.includes('.')) {
                return {
                    success: false,
                    error: 'Please enter a valid email address'
                };
            }

            if (password.length < 6) {
                return {
                    success: false,
                    error: 'Password must be at least 6 characters long'
                };
            }

            if (username.length < 2) {
                return {
                    success: false,
                    error: 'Username must be at least 2 characters long'
                };
            }

            if (username.length > 30) {
                return {
                    success: false,
                    error: 'Username must be less than 30 characters'
                };
            }

            // Check for valid username characters
            if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
                return {
                    success: false,
                    error: 'Username can only contain letters, numbers, underscores, and hyphens'
                };
            }

            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            // Update user profile with username
            await updateProfile(user, {
                displayName: username
            });

            return {
                success: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: username,
                    displayName: user.displayName,
                    emailVerified: user.emailVerified
                }
            };
        } catch (error) {
            console.error('Sign up error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign in with email and password
    async signIn(email, password) {
        try {
            // Validate inputs
            if (!email || !password) {
                return {
                    success: false,
                    error: 'Email and password are required'
                };
            }

            if (!email.includes('@') || !email.includes('.')) {
                return {
                    success: false,
                    error: 'Please enter a valid email address'
                };
            }

            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            return {
                success: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName,
                    emailVerified: user.emailVerified,
                    lastSignInTime: user.metadata.lastSignInTime,
                    creationTime: user.metadata.creationTime
                }
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign out
    async signOut() {
        try {
            await signOut(auth);
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return {
                success: false,
                error: 'Failed to sign out'
            };
        }
    },

    // Listen for authentication state changes
    onAuthStateChanged(callback) {
        if (!auth) {
            console.error('Firebase auth not initialized');
            return () => {};
        }
        
        return onAuthStateChanged(auth, (user) => {
            if (user) {
                callback({
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName
                });
            } else {
                callback(null);
            }
        });
    },

    // Get Firebase ID token for server authentication
    async getIdToken() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                throw new Error('No authenticated user');
            }

            return await user.getIdToken();
        } catch (error) {
            console.error('Error getting ID token:', error);
            return null;
        }
    },

    // Check if user is authenticated
    isAuthenticated() {
        return auth?.currentUser !== null;
    },

    // Get current authentication state
    async getAuthState() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                return { authenticated: false, user: null };
            }

            return {
                authenticated: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName,
                    emailVerified: user.emailVerified,
                    lastSignInTime: user.metadata.lastSignInTime,
                    creationTime: user.metadata.creationTime
                }
            };
        } catch (error) {
            console.error('Error getting auth state:', error);
            return { authenticated: false, user: null, error: error.message };
        }
    },

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Validate password strength
    validatePassword(password) {
        const errors = [];

        if (!password) {
            errors.push('Password is required');
        } else {
            if (password.length < 6) {
                errors.push('Password must be at least 6 characters long');
            }
            if (password.length > 128) {
                errors.push('Password must be less than 128 characters');
            }
            if (!/[a-zA-Z]/.test(password)) {
                errors.push('Password must contain at least one letter');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // Validate username
    validateUsername(username) {
        const errors = [];

        if (!username) {
            errors.push('Username is required');
        } else {
            if (username.length < 2) {
                errors.push('Username must be at least 2 characters long');
            }
            if (username.length > 30) {
                errors.push('Username must be less than 30 characters');
            }
            if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
                errors.push('Username can only contain letters, numbers, underscores, and hyphens');
            }
            if (/^[_-]/.test(username) || /[_-]$/.test(username)) {
                errors.push('Username cannot start or end with underscore or hyphen');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }
};

// Helper function to convert Firebase error codes to user-friendly messages
function getFirebaseErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address. Please check your email or create a new account.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again or reset your password.';
        case 'auth/email-already-in-use':
            return 'An account with this email already exists. Please sign in instead.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long and include a mix of letters and numbers.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address (e.g., <EMAIL>).';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please wait a few minutes before trying again.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your internet connection and try again.';
        case 'auth/user-disabled':
            return 'This account has been disabled. Please contact support for assistance.';
        case 'auth/invalid-credential':
            return 'Invalid email or password. Please check your credentials and try again.';
        case 'auth/operation-not-allowed':
            return 'Email/password authentication is not enabled. Please contact support.';
        case 'auth/requires-recent-login':
            return 'This operation requires recent authentication. Please sign in again.';
        case 'auth/credential-already-in-use':
            return 'This credential is already associated with a different account.';
        case 'auth/invalid-verification-code':
            return 'Invalid verification code. Please check and try again.';
        case 'auth/invalid-verification-id':
            return 'Invalid verification ID. Please restart the verification process.';
        case 'auth/missing-verification-code':
            return 'Please enter the verification code.';
        case 'auth/missing-verification-id':
            return 'Verification ID is missing. Please restart the verification process.';
        case 'auth/code-expired':
            return 'The verification code has expired. Please request a new one.';
        case 'auth/invalid-phone-number':
            return 'Please enter a valid phone number.';
        case 'auth/missing-phone-number':
            return 'Please enter your phone number.';
        case 'auth/quota-exceeded':
            return 'SMS quota exceeded. Please try again later.';
        case 'auth/cancelled-popup-request':
            return 'Authentication was cancelled. Please try again.';
        case 'auth/popup-blocked':
            return 'Popup was blocked by your browser. Please allow popups and try again.';
        case 'auth/popup-closed-by-user':
            return 'Authentication popup was closed. Please try again.';
        case 'auth/unauthorized-domain':
            return 'This domain is not authorized for authentication. Please contact support.';
        case 'auth/invalid-action-code':
            return 'Invalid or expired action code. Please request a new one.';
        case 'auth/expired-action-code':
            return 'The action code has expired. Please request a new one.';
        case 'auth/invalid-continue-uri':
            return 'Invalid continue URL. Please contact support.';
        case 'auth/missing-continue-uri':
            return 'Continue URL is missing. Please contact support.';
        case 'auth/missing-email':
            return 'Please enter your email address.';
        case 'auth/missing-password':
            return 'Please enter your password.';
        case 'auth/internal-error':
            return 'An internal error occurred. Please try again later.';
        case 'auth/timeout':
            return 'Request timed out. Please check your connection and try again.';
        default:
            // Log unknown error codes for debugging
            console.warn('Unknown Firebase error code:', errorCode);
            return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }
}

// Export for global access
window.FirebaseAuth = FirebaseAuth;
