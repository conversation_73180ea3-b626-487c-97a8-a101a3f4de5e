// Firebase Configuration and Authentication
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
import { 
    getAuth, 
    createUserWithEmailAndPassword, 
    signInWithEmailAndPassword, 
    signOut, 
    onAuthStateChanged,
    updateProfile
} from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

// Firebase configuration - these values will be loaded from the server
let firebaseConfig = {};

// Firebase app and auth instances
let app;
let auth;

// Initialize Firebase
async function initializeFirebase() {
    try {
        // Fetch Firebase config from server
        const response = await fetch('/api/firebase-config');
        if (!response.ok) {
            throw new Error('Failed to fetch Firebase configuration');
        }
        
        firebaseConfig = await response.json();
        
        // Initialize Firebase
        app = initializeApp(firebaseConfig);
        auth = getAuth(app);
        
        console.log('Firebase initialized successfully');
        return true;
    } catch (error) {
        console.error('Error initializing Firebase:', error);
        return false;
    }
}

// Authentication functions
export const FirebaseAuth = {
    // Initialize Firebase
    async init() {
        return await initializeFirebase();
    },

    // Get current user
    getCurrentUser() {
        return auth?.currentUser || null;
    },

    // Sign up with email and password
    async signUp(email, password, username) {
        try {
            // Validate inputs
            if (!email || !password || !username) {
                return {
                    success: false,
                    error: 'All fields are required'
                };
            }

            if (!email.includes('@') || !email.includes('.')) {
                return {
                    success: false,
                    error: 'Please enter a valid email address'
                };
            }

            if (password.length < 6) {
                return {
                    success: false,
                    error: 'Password must be at least 6 characters long'
                };
            }

            if (username.length < 2) {
                return {
                    success: false,
                    error: 'Username must be at least 2 characters long'
                };
            }

            if (username.length > 30) {
                return {
                    success: false,
                    error: 'Username must be less than 30 characters'
                };
            }

            // Check for valid username characters
            if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
                return {
                    success: false,
                    error: 'Username can only contain letters, numbers, underscores, and hyphens'
                };
            }

            const userCredential = await createUserWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            // Update user profile with username
            await updateProfile(user, {
                displayName: username
            });

            // Get ID token and store session
            const idToken = await user.getIdToken();
            const userData = {
                uid: user.uid,
                email: user.email,
                username: username,
                displayName: user.displayName,
                emailVerified: user.emailVerified
            };

            this.storeSession(userData, idToken);
            this.startTokenRefreshTimer();

            return {
                success: true,
                user: userData
            };
        } catch (error) {
            console.error('Sign up error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign in with email and password
    async signIn(email, password) {
        try {
            // Validate inputs
            if (!email || !password) {
                return {
                    success: false,
                    error: 'Email and password are required'
                };
            }

            if (!email.includes('@') || !email.includes('.')) {
                return {
                    success: false,
                    error: 'Please enter a valid email address'
                };
            }

            const userCredential = await signInWithEmailAndPassword(auth, email, password);
            const user = userCredential.user;

            // Get ID token and store session
            const idToken = await user.getIdToken();
            const userData = {
                uid: user.uid,
                email: user.email,
                username: user.displayName || user.email.split('@')[0],
                displayName: user.displayName,
                emailVerified: user.emailVerified,
                lastSignInTime: user.metadata.lastSignInTime,
                creationTime: user.metadata.creationTime
            };

            this.storeSession(userData, idToken);
            this.startTokenRefreshTimer();

            return {
                success: true,
                user: userData
            };
        } catch (error) {
            console.error('Sign in error:', error);
            return {
                success: false,
                error: getFirebaseErrorMessage(error.code)
            };
        }
    },

    // Sign out
    async signOut() {
        try {
            // Stop token refresh timer
            this.stopTokenRefreshTimer();

            // Clear local session data
            this.clearSession();

            // Sign out from Firebase
            await signOut(auth);

            console.log('User signed out successfully');
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);

            // Even if Firebase sign out fails, clear local data
            this.clearSession();
            this.stopTokenRefreshTimer();

            return {
                success: false,
                error: 'Failed to sign out completely, but local session cleared'
            };
        }
    },

    // Listen for authentication state changes
    onAuthStateChanged(callback) {
        if (!auth) {
            console.error('Firebase auth not initialized');
            return () => {};
        }

        return onAuthStateChanged(auth, async (user) => {
            if (user) {
                try {
                    // Get fresh token and update session
                    const idToken = await user.getIdToken();
                    const userData = {
                        uid: user.uid,
                        email: user.email,
                        username: user.displayName || user.email.split('@')[0],
                        displayName: user.displayName,
                        emailVerified: user.emailVerified,
                        lastSignInTime: user.metadata.lastSignInTime,
                        creationTime: user.metadata.creationTime
                    };

                    this.storeSession(userData, idToken);
                    this.startTokenRefreshTimer();

                    callback(userData);
                } catch (error) {
                    console.error('Error handling auth state change:', error);
                    callback({
                        uid: user.uid,
                        email: user.email,
                        username: user.displayName || user.email.split('@')[0],
                        displayName: user.displayName
                    });
                }
            } else {
                // User signed out
                this.clearSession();
                this.stopTokenRefreshTimer();
                callback(null);
            }
        });
    },

    // Get Firebase ID token for server authentication
    async getIdToken() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                throw new Error('No authenticated user');
            }

            return await user.getIdToken();
        } catch (error) {
            console.error('Error getting ID token:', error);
            return null;
        }
    },

    // Check if user is authenticated
    isAuthenticated() {
        return auth?.currentUser !== null;
    },

    // Get current authentication state
    async getAuthState() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                return { authenticated: false, user: null };
            }

            return {
                authenticated: true,
                user: {
                    uid: user.uid,
                    email: user.email,
                    username: user.displayName || user.email.split('@')[0],
                    displayName: user.displayName,
                    emailVerified: user.emailVerified,
                    lastSignInTime: user.metadata.lastSignInTime,
                    creationTime: user.metadata.creationTime
                }
            };
        } catch (error) {
            console.error('Error getting auth state:', error);
            return { authenticated: false, user: null, error: error.message };
        }
    },

    // Validate email format
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    },

    // Validate password strength
    validatePassword(password) {
        const errors = [];

        if (!password) {
            errors.push('Password is required');
        } else {
            if (password.length < 6) {
                errors.push('Password must be at least 6 characters long');
            }
            if (password.length > 128) {
                errors.push('Password must be less than 128 characters');
            }
            if (!/[a-zA-Z]/.test(password)) {
                errors.push('Password must contain at least one letter');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // Validate username
    validateUsername(username) {
        const errors = [];

        if (!username) {
            errors.push('Username is required');
        } else {
            if (username.length < 2) {
                errors.push('Username must be at least 2 characters long');
            }
            if (username.length > 30) {
                errors.push('Username must be less than 30 characters');
            }
            if (!/^[a-zA-Z0-9_-]+$/.test(username)) {
                errors.push('Username can only contain letters, numbers, underscores, and hyphens');
            }
            if (/^[_-]/.test(username) || /[_-]$/.test(username)) {
                errors.push('Username cannot start or end with underscore or hyphen');
            }
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    },

    // Session Management Functions

    // Store session data securely
    storeSession(user, idToken) {
        try {
            const sessionData = {
                user: user,
                token: idToken,
                timestamp: Date.now(),
                expiresAt: Date.now() + (60 * 60 * 1000) // 1 hour from now
            };

            localStorage.setItem('currentUser', JSON.stringify(user));
            localStorage.setItem('authToken', idToken);
            localStorage.setItem('sessionData', JSON.stringify(sessionData));

            console.log('Session stored successfully');
            return true;
        } catch (error) {
            console.error('Error storing session:', error);
            return false;
        }
    },

    // Get current session
    getSession() {
        try {
            const sessionDataStr = localStorage.getItem('sessionData');
            if (!sessionDataStr) {
                return null;
            }

            const sessionData = JSON.parse(sessionDataStr);

            // Check if session has expired
            if (Date.now() > sessionData.expiresAt) {
                console.log('Session expired, clearing data');
                this.clearSession();
                return null;
            }

            return sessionData;
        } catch (error) {
            console.error('Error getting session:', error);
            return null;
        }
    },

    // Clear session data
    clearSession() {
        try {
            localStorage.removeItem('currentUser');
            localStorage.removeItem('authToken');
            localStorage.removeItem('sessionData');
            sessionStorage.clear();

            console.log('Session cleared successfully');
            return true;
        } catch (error) {
            console.error('Error clearing session:', error);
            return false;
        }
    },

    // Refresh authentication token
    async refreshToken() {
        try {
            const user = auth?.currentUser;
            if (!user) {
                throw new Error('No authenticated user');
            }

            // Force refresh the token
            const newToken = await user.getIdToken(true);

            // Update stored session
            const currentSession = this.getSession();
            if (currentSession) {
                currentSession.token = newToken;
                currentSession.timestamp = Date.now();
                currentSession.expiresAt = Date.now() + (60 * 60 * 1000); // 1 hour from now

                localStorage.setItem('authToken', newToken);
                localStorage.setItem('sessionData', JSON.stringify(currentSession));
            }

            console.log('Token refreshed successfully');
            return newToken;
        } catch (error) {
            console.error('Error refreshing token:', error);
            return null;
        }
    },

    // Check if session is valid and refresh if needed
    async validateSession() {
        try {
            const session = this.getSession();
            if (!session) {
                return { valid: false, reason: 'No session found' };
            }

            // Check if token is close to expiring (within 5 minutes)
            const timeUntilExpiry = session.expiresAt - Date.now();
            const fiveMinutes = 5 * 60 * 1000;

            if (timeUntilExpiry < fiveMinutes) {
                console.log('Token expiring soon, attempting refresh...');
                const newToken = await this.refreshToken();

                if (!newToken) {
                    return { valid: false, reason: 'Token refresh failed' };
                }
            }

            // Verify the user is still authenticated with Firebase
            const user = auth?.currentUser;
            if (!user) {
                return { valid: false, reason: 'No Firebase user' };
            }

            return { valid: true, user: session.user, token: session.token };
        } catch (error) {
            console.error('Error validating session:', error);
            return { valid: false, reason: 'Validation error', error: error.message };
        }
    },

    // Auto-refresh token periodically
    startTokenRefreshTimer() {
        // Clear any existing timer
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        // Set up timer to refresh token every 50 minutes
        this.refreshTimer = setInterval(async () => {
            const session = this.getSession();
            if (session && auth?.currentUser) {
                console.log('Auto-refreshing token...');
                await this.refreshToken();
            }
        }, 50 * 60 * 1000); // 50 minutes

        console.log('Token refresh timer started');
    },

    // Stop token refresh timer
    stopTokenRefreshTimer() {
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
            this.refreshTimer = null;
            console.log('Token refresh timer stopped');
        }
    }
};

// Helper function to convert Firebase error codes to user-friendly messages
function getFirebaseErrorMessage(errorCode) {
    switch (errorCode) {
        case 'auth/user-not-found':
            return 'No account found with this email address. Please check your email or create a new account.';
        case 'auth/wrong-password':
            return 'Incorrect password. Please try again or reset your password.';
        case 'auth/email-already-in-use':
            return 'An account with this email already exists. Please sign in instead.';
        case 'auth/weak-password':
            return 'Password should be at least 6 characters long and include a mix of letters and numbers.';
        case 'auth/invalid-email':
            return 'Please enter a valid email address (e.g., <EMAIL>).';
        case 'auth/too-many-requests':
            return 'Too many failed attempts. Please wait a few minutes before trying again.';
        case 'auth/network-request-failed':
            return 'Network error. Please check your internet connection and try again.';
        case 'auth/user-disabled':
            return 'This account has been disabled. Please contact support for assistance.';
        case 'auth/invalid-credential':
            return 'Invalid email or password. Please check your credentials and try again.';
        case 'auth/operation-not-allowed':
            return 'Email/password authentication is not enabled. Please contact support.';
        case 'auth/requires-recent-login':
            return 'This operation requires recent authentication. Please sign in again.';
        case 'auth/credential-already-in-use':
            return 'This credential is already associated with a different account.';
        case 'auth/invalid-verification-code':
            return 'Invalid verification code. Please check and try again.';
        case 'auth/invalid-verification-id':
            return 'Invalid verification ID. Please restart the verification process.';
        case 'auth/missing-verification-code':
            return 'Please enter the verification code.';
        case 'auth/missing-verification-id':
            return 'Verification ID is missing. Please restart the verification process.';
        case 'auth/code-expired':
            return 'The verification code has expired. Please request a new one.';
        case 'auth/invalid-phone-number':
            return 'Please enter a valid phone number.';
        case 'auth/missing-phone-number':
            return 'Please enter your phone number.';
        case 'auth/quota-exceeded':
            return 'SMS quota exceeded. Please try again later.';
        case 'auth/cancelled-popup-request':
            return 'Authentication was cancelled. Please try again.';
        case 'auth/popup-blocked':
            return 'Popup was blocked by your browser. Please allow popups and try again.';
        case 'auth/popup-closed-by-user':
            return 'Authentication popup was closed. Please try again.';
        case 'auth/unauthorized-domain':
            return 'This domain is not authorized for authentication. Please contact support.';
        case 'auth/invalid-action-code':
            return 'Invalid or expired action code. Please request a new one.';
        case 'auth/expired-action-code':
            return 'The action code has expired. Please request a new one.';
        case 'auth/invalid-continue-uri':
            return 'Invalid continue URL. Please contact support.';
        case 'auth/missing-continue-uri':
            return 'Continue URL is missing. Please contact support.';
        case 'auth/missing-email':
            return 'Please enter your email address.';
        case 'auth/missing-password':
            return 'Please enter your password.';
        case 'auth/internal-error':
            return 'An internal error occurred. Please try again later.';
        case 'auth/timeout':
            return 'Request timed out. Please check your connection and try again.';
        default:
            // Log unknown error codes for debugging
            console.warn('Unknown Firebase error code:', errorCode);
            return 'An unexpected error occurred. Please try again or contact support if the problem persists.';
    }
}

// Export for global access
window.FirebaseAuth = FirebaseAuth;
