<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f8f6f3 0%, #ffffff 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 100vw;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
        }

        /* Header */
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 20px 30px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 32px;
            font-weight: 700;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-buttons {
            display: flex;
            gap: 15px;
        }

        .nav-btn {
            background: transparent;
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        .nav-btn.primary {
            background: #8B4513;
            color: white;
        }

        .nav-btn.primary:hover {
            background: #7a3c0f;
        }

        /* Main Content */
        .main-content {
            display: grid;
            grid-template-columns: 1fr 2fr 1fr;
            gap: 30px;
            align-items: start;
            min-height: calc(100vh - 140px);
        }

        .sidebar {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .sidebar h3 {
            margin-bottom: 20px;
            color: #333;
            font-size: 18px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 10px;
            padding: 12px 15px;
            margin-bottom: 8px;
        }

        .sidebar-item:hover {
            background: rgba(139, 69, 19, 0.1);
            transform: translateX(5px);
        }

        .sidebar-item.active {
            background: rgba(139, 69, 19, 0.15);
            color: #8B4513;
            font-weight: 600;
        }

        .sidebar-item::before {
            content: "📱";
            margin-right: 12px;
            font-size: 16px;
        }

        .sidebar-item:nth-child(2)::before { content: "🏠"; }
        .sidebar-item:nth-child(3)::before { content: "🔍"; }
        .sidebar-item:nth-child(4)::before { content: "💬"; }
        .sidebar-item:nth-child(5)::before { content: "👤"; }

        /* Feed Section */
        .feed-section {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .feed-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .feed-title {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .refresh-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }

        .refresh-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.4);
        }

        .create-post {
            background: rgba(201, 168, 118, 0.1);
            border-radius: 20px;
            padding: 25px;
            margin-bottom: 30px;
            border: 2px dashed rgba(201, 168, 118, 0.3);
            text-align: center;
        }

        .create-post-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        .create-post h3 {
            color: #c9a876;
            margin-bottom: 10px;
            font-size: 20px;
        }

        .create-post p {
            color: #666;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .create-btn {
            background: linear-gradient(45deg, #c9a876, #f4e4bc);
            color: #1a1a1a;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .create-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(201, 168, 118, 0.4);
        }

        .load-more {
            background: rgba(139, 69, 19, 0.05);
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 15px 40px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 30px;
            width: 100%;
        }

        .load-more:hover {
            background: #8B4513;
            color: white;
            transform: translateY(-2px);
        }

        /* Right Sidebar */
        .trending {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .trending-item {
            padding: 15px 0;
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .trending-item:hover {
            background: rgba(201, 168, 118, 0.05);
            border-radius: 10px;
            padding: 15px;
            margin: 0 -15px;
        }

        .trending-item:last-child {
            border-bottom: none;
        }

        .trending-topic {
            font-weight: 600;
            color: #c9a876;
            margin-bottom: 5px;
        }

        .trending-count {
            font-size: 12px;
            color: #999;
        }

        /* Mobile Bottom Navigation */
        .mobile-nav {
            display: none;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 12px 0;
            border-top: 1px solid rgba(139, 69, 19, 0.1);
            box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .mobile-nav-items {
            display: flex;
            justify-content: space-around;
            align-items: center;
            max-width: 600px;
            margin: 0 auto;
            padding: 0 10px;
        }

        .mobile-nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 6px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-radius: 12px;
            min-width: 50px;
            flex: 1;
            max-width: 80px;
        }

        .mobile-nav-item:hover,
        .mobile-nav-item.active {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
            transform: translateY(-2px);
        }

        .mobile-nav-item .icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .mobile-nav-item .label {
            font-size: 11px;
            font-weight: 500;
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .main-content {
                grid-template-columns: 1fr 2fr;
            }
            
            .trending {
                display: none;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
                padding-bottom: 90px;
            }

            .header {
                padding: 15px 20px;
                border-radius: 15px;
                margin-bottom: 20px;
            }

            .logo {
                font-size: 24px;
            }

            .nav-buttons {
                gap: 10px;
            }

            .nav-btn {
                padding: 8px 15px;
                font-size: 14px;
                border-radius: 20px;
            }

            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .sidebar {
                display: none;
            }

            .feed-section {
                padding: 20px;
                border-radius: 15px;
            }

            .feed-title {
                font-size: 20px;
            }

            .refresh-btn {
                padding: 10px 20px;
                font-size: 14px;
            }

            .create-post {
                padding: 20px;
                border-radius: 15px;
                margin-bottom: 20px;
            }

            .create-post-icon {
                font-size: 40px;
                margin-bottom: 12px;
            }

            .create-post h3 {
                font-size: 18px;
                margin-bottom: 8px;
            }

            .create-post p {
                font-size: 13px;
                margin-bottom: 15px;
            }

            .create-btn {
                padding: 12px 25px;
                font-size: 14px;
                border-radius: 20px;
            }

            .load-more {
                padding: 12px 30px;
                border-radius: 20px;
                margin-top: 20px;
            }

            .mobile-nav {
                display: block;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                padding-bottom: 90px;
            }

            .header {
                padding: 12px 15px;
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .nav-buttons {
                width: 100%;
                justify-content: center;
            }

            .feed-section {
                padding: 15px;
            }

            .feed-header {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                margin-bottom: 20px;
            }

            .create-post {
                padding: 15px;
            }

            .create-post-icon {
                font-size: 36px;
            }

            .create-post h3 {
                font-size: 16px;
            }

            .create-post p {
                font-size: 12px;
            }

            /* Profile responsive styles */
            .profile-header {
                flex-direction: column;
                align-items: center;
                text-align: center;
                gap: 20px;
            }

            .profile-avatar {
                width: 100px;
                height: 100px;
            }

            .avatar-placeholder {
                font-size: 40px;
            }

            .profile-name-section h3 {
                font-size: 24px;
            }

            .profile-stats {
                justify-content: center;
                gap: 20px;
            }

            .profile-tabs {
                flex-wrap: wrap;
            }

            .profile-tab {
                flex: 1;
                min-width: 80px;
                padding: 12px 10px;
                font-size: 14px;
            }

            /* Post modal responsive styles */
            .post-modal {
                padding: 10px;
            }

            .post-modal-content {
                max-width: 100%;
                border-radius: 15px;
            }

            .post-modal-header {
                padding: 20px 20px 15px;
            }

            .post-modal-header h3 {
                font-size: 20px;
            }

            .post-modal-body {
                padding: 15px 20px 20px;
            }

            .post-modal .form-group {
                margin-bottom: 20px;
            }

            .media-upload-area {
                padding: 20px;
            }

            .upload-placeholder .upload-icon {
                font-size: 36px;
            }

            .post-modal-footer {
                flex-direction: column;
                gap: 10px;
            }

            .btn-primary,
            .btn-secondary {
                width: 100%;
                padding: 14px 20px;
            }

            /* Post display responsive styles */
            .post-card {
                padding: 20px;
                border-radius: 15px;
            }

            .post-header {
                gap: 12px;
                margin-bottom: 15px;
            }

            .post-avatar {
                width: 45px;
                height: 45px;
                font-size: 16px;
            }

            .post-title {
                font-size: 18px;
                margin-bottom: 10px;
            }

            .post-text {
                font-size: 15px;
            }

            .post-actions-left {
                gap: 15px;
            }

            .post-action {
                padding: 6px 10px;
                font-size: 13px;
            }

            .post-media-grid img {
                height: 150px;
            }

            .posts-container {
                gap: 20px;
            }
        }

        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .feed-section, .sidebar, .trending {
            animation: fadeIn 0.6s ease-out;
        }

        .feed-section {
            animation-delay: 0.1s;
        }

        .sidebar {
            animation-delay: 0.2s;
        }

        .trending {
            animation-delay: 0.3s;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Content Section Switching */
        .content-section {
            display: none;
            animation: fadeIn 0.6s ease-out;
        }

        .content-section.active {
            display: block;
        }

        /* Ensure consistent styling across all sections */
        .explore-section,
        .messages-section,
        .settings-section {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-right: 24px;
        }

        /* Profile Section Styles */
        .profile-section {
            flex: 1;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-right: 24px;
        }

        .profile-header {
            display: flex;
            gap: 30px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .profile-avatar-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 15px;
        }

        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.2);
        }

        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-placeholder {
            font-size: 48px;
            color: white;
        }

        .change-avatar-btn {
            background: transparent;
            border: 2px solid #8B4513;
            color: #8B4513;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .change-avatar-btn:hover {
            background: #8B4513;
            color: white;
        }

        .profile-info {
            flex: 1;
        }

        .profile-name-section {
            margin-bottom: 20px;
        }

        .profile-name-section h3 {
            font-size: 28px;
            font-weight: 700;
            color: #333;
            margin-bottom: 5px;
        }

        .profile-username {
            font-size: 16px;
            color: #8B4513;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .profile-email {
            font-size: 14px;
            color: #666;
        }

        .profile-stats {
            display: flex;
            gap: 30px;
            margin-bottom: 20px;
        }

        .stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
        }

        .stat-number {
            font-size: 24px;
            font-weight: 700;
            color: #333;
        }

        .stat-label {
            font-size: 14px;
            color: #666;
            margin-top: 2px;
        }

        .edit-profile-btn {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .edit-profile-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
        }

        .profile-bio-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(139, 69, 19, 0.05);
            border-radius: 15px;
        }

        .profile-bio-section h4 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .profile-bio-section p {
            color: #666;
            line-height: 1.6;
        }

        .profile-tabs {
            display: flex;
            border-bottom: 2px solid #f0f0f0;
            margin-bottom: 20px;
        }

        .profile-tab {
            background: none;
            border: none;
            padding: 15px 20px;
            font-weight: 600;
            color: #666;
            cursor: pointer;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .profile-tab:hover {
            color: #8B4513;
        }

        .profile-tab.active {
            color: #8B4513;
            border-bottom-color: #8B4513;
        }

        .profile-tab-content {
            min-height: 300px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* Post Creation Modal Styles */
        .post-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
            z-index: 2000;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .post-modal-content {
            background: white;
            border-radius: 20px;
            width: 100%;
            max-width: 600px;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        .post-modal-header {
            padding: 25px 30px 20px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .post-modal-header h3 {
            font-size: 24px;
            font-weight: 700;
            color: #333;
            margin: 0;
        }

        .post-modal-body {
            padding: 25px 30px;
        }

        .post-modal .form-group {
            margin-bottom: 25px;
        }

        .post-modal .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .post-modal input[type="text"],
        .post-modal textarea,
        .post-modal select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #fafafa;
            font-family: inherit;
        }

        .post-modal input[type="text"]:focus,
        .post-modal textarea:focus,
        .post-modal select:focus {
            outline: none;
            border-color: #8B4513;
            background: white;
            box-shadow: 0 0 0 3px rgba(139, 69, 19, 0.1);
        }

        .post-modal textarea {
            resize: vertical;
            min-height: 120px;
        }

        .char-count {
            text-align: right;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .char-count.warning {
            color: #f39c12;
        }

        .char-count.danger {
            color: #e74c3c;
        }

        .media-upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: #fafafa;
        }

        .media-upload-area:hover {
            border-color: #8B4513;
            background: rgba(139, 69, 19, 0.05);
        }

        .media-upload-area.dragover {
            border-color: #8B4513;
            background: rgba(139, 69, 19, 0.1);
        }

        .upload-placeholder .upload-icon {
            font-size: 48px;
            margin-bottom: 10px;
            opacity: 0.6;
        }

        .upload-placeholder p {
            font-size: 16px;
            color: #333;
            margin-bottom: 5px;
        }

        .upload-placeholder small {
            color: #666;
            font-size: 12px;
        }

        .media-preview {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }

        .media-preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 8px;
            overflow: hidden;
            border: 2px solid #e0e0e0;
        }

        .media-preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .media-preview-item .remove-media {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(231, 76, 60, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .post-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 10px;
            width: 18px;
            height: 18px;
        }

        .post-modal-footer {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .btn-primary {
            background: linear-gradient(45deg, #8B4513, #D2691E);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(139, 69, 19, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: #8B4513;
            border: 2px solid #8B4513;
            padding: 10px 22px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-secondary:hover {
            background: #8B4513;
            color: white;
        }

        .post-loading,
        .post-error {
            display: none;
            text-align: center;
            padding: 40px 30px;
        }

        .post-loading .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f0f0f0;
            border-top: 4px solid #8B4513;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .post-error .error-icon {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.6;
        }

        /* Post Display System Styles */
        .posts-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
            margin-bottom: 30px;
        }

        .post-card {
            background: white;
            border-radius: 20px;
            padding: 25px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(139, 69, 19, 0.1);
            transition: all 0.3s ease;
            animation: fadeInUp 0.6s ease-out;
        }

        .post-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
        }

        .post-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            gap: 15px;
        }

        .post-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(45deg, #8B4513, #D2691E);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 18px;
            flex-shrink: 0;
        }

        .post-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .post-user-info {
            flex: 1;
        }

        .post-username {
            font-weight: 700;
            color: #333;
            font-size: 16px;
            margin-bottom: 2px;
        }

        .post-meta {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 14px;
            color: #666;
        }

        .post-category {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }

        .post-time {
            color: #999;
        }

        .post-menu {
            position: relative;
        }

        .post-menu-btn {
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .post-menu-btn:hover {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
        }

        .post-content {
            margin-bottom: 20px;
        }

        .post-title {
            font-size: 20px;
            font-weight: 700;
            color: #333;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .post-text {
            color: #444;
            line-height: 1.6;
            font-size: 16px;
            white-space: pre-wrap;
        }

        .post-media {
            margin: 20px 0;
            border-radius: 15px;
            overflow: hidden;
        }

        .post-media img {
            width: 100%;
            height: auto;
            display: block;
        }

        .post-media-grid {
            display: grid;
            gap: 8px;
            border-radius: 15px;
            overflow: hidden;
        }

        .post-media-grid.grid-2 {
            grid-template-columns: 1fr 1fr;
        }

        .post-media-grid.grid-3 {
            grid-template-columns: 2fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        .post-media-grid.grid-3 img:first-child {
            grid-row: 1 / 3;
        }

        .post-media-grid.grid-4 {
            grid-template-columns: 1fr 1fr;
            grid-template-rows: 1fr 1fr;
        }

        .post-media-grid img {
            width: 100%;
            height: 200px;
            object-fit: cover;
        }

        .post-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 15px 0;
        }

        .post-tag {
            background: rgba(201, 168, 118, 0.15);
            color: #8B4513;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 13px;
            font-weight: 500;
        }

        .post-actions {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 20px;
            border-top: 1px solid #f0f0f0;
        }

        .post-actions-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .post-action {
            display: flex;
            align-items: center;
            gap: 8px;
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
            font-weight: 500;
        }

        .post-action:hover {
            background: rgba(139, 69, 19, 0.1);
            color: #8B4513;
        }

        .post-action.liked {
            color: #e74c3c;
        }

        .post-action.liked:hover {
            background: rgba(231, 76, 60, 0.1);
        }

        .post-action-icon {
            font-size: 16px;
        }

        .post-stats {
            display: flex;
            align-items: center;
            gap: 15px;
            font-size: 14px;
            color: #666;
        }

        .empty-state {
            display: block;
        }

        .posts-loading {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="logo">Naroop</div>
            <div class="nav-buttons">
                <button class="nav-btn primary">Log Out</button>
            </div>
        </header>

        <main class="main-content">
            <aside class="sidebar">
                <h3>Navigation</h3>
                <div class="sidebar-item active" data-section="feed">Feed</div>
                <div class="sidebar-item" data-section="explore">Explore</div>
                <div class="sidebar-item" data-section="messages">Messages</div>
                <div class="sidebar-item" data-section="profile">Profile</div>
                <div class="sidebar-item" data-section="settings">Settings</div>
            </aside>

            <!-- Feed Section -->
            <section class="content-section feed-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>

                <div class="create-post">
                    <div class="create-post-icon">📝</div>
                    <h3>Share Your Story</h3>
                    <p>Connect with our community by sharing experiences that uplift and inspire our people</p>
                    <button class="create-btn">Create Post</button>
                </div>

                <!-- Posts Container -->
                <div id="postsContainer" class="posts-container">
                    <!-- Posts will be dynamically loaded here -->
                </div>

                <!-- Empty State -->
                <div id="emptyState" class="empty-state" style="text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;">📖</div>
                    <h3 style="margin-bottom: 10px; color: #333;">No stories yet</h3>
                    <p>Be the first to share your positive experience!</p>
                </div>

                <!-- Loading State -->
                <div id="postsLoading" class="posts-loading" style="display: none; text-align: center; padding: 40px 0;">
                    <div class="loading-spinner" style="margin: 0 auto 20px;"></div>
                    <p>Loading stories...</p>
                </div>

                <!-- Load More Button -->
                <button id="loadMoreBtn" class="load-more" style="display: none;">Load More Stories</button>
            </section>

            <!-- Explore Section -->
            <section class="content-section explore-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div style="text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;">🔍</div>
                    <h3 style="margin-bottom: 10px; color: #333;">Discover New Stories</h3>
                    <p>Explore trending content and discover new voices in our community</p>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="content-section messages-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                    <button class="refresh-btn">Refresh</button>
                </div>
                <div style="text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;">💬</div>
                    <h3 style="margin-bottom: 10px; color: #333;">No Messages</h3>
                    <p>Connect with other community members through direct messages</p>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="content-section profile-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                    <button class="refresh-btn" id="profileRefreshBtn">Refresh</button>
                </div>

                <!-- Profile Content -->
                <div id="profileContent">
                    <!-- Profile Header -->
                    <div class="profile-header">
                        <div class="profile-avatar-section">
                            <div class="profile-avatar" id="profileAvatar">
                                <img id="profileAvatarImg" src="" alt="Profile Avatar" style="display: none;">
                                <div class="avatar-placeholder" id="avatarPlaceholder">👤</div>
                            </div>
                            <button class="change-avatar-btn" id="changeAvatarBtn">Change Photo</button>
                            <input type="file" id="avatarInput" accept="image/*" style="display: none;">
                        </div>

                        <div class="profile-info">
                            <div class="profile-name-section">
                                <h3 id="profileDisplayName">Loading...</h3>
                                <p class="profile-username" id="profileUsername">@loading</p>
                                <p class="profile-email" id="profileEmail"><EMAIL></p>
                            </div>

                            <div class="profile-stats">
                                <div class="stat">
                                    <span class="stat-number" id="profileStories">0</span>
                                    <span class="stat-label">Stories</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-number" id="profileFollowers">0</span>
                                    <span class="stat-label">Followers</span>
                                </div>
                                <div class="stat">
                                    <span class="stat-number" id="profileFollowing">0</span>
                                    <span class="stat-label">Following</span>
                                </div>
                            </div>

                            <button class="edit-profile-btn" id="editProfileBtn">Edit Profile</button>
                        </div>
                    </div>

                    <!-- Profile Bio -->
                    <div class="profile-bio-section">
                        <h4>About</h4>
                        <p id="profileBio">Loading bio...</p>
                    </div>

                    <!-- Profile Tabs -->
                    <div class="profile-tabs">
                        <button class="profile-tab active" data-tab="stories">My Stories</button>
                        <button class="profile-tab" data-tab="liked">Liked Posts</button>
                        <button class="profile-tab" data-tab="activity">Activity</button>
                    </div>

                    <!-- Profile Tab Content -->
                    <div class="profile-tab-content">
                        <div class="tab-pane active" id="stories-tab">
                            <div class="profile-stories" id="profileStories">
                                <div style="text-align: center; padding: 40px 0; color: #666;">
                                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">📝</div>
                                    <h4 style="margin-bottom: 8px; color: #333;">No stories yet</h4>
                                    <p>Share your first story to get started!</p>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="liked-tab">
                            <div class="profile-liked" id="profileLiked">
                                <div style="text-align: center; padding: 40px 0; color: #666;">
                                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">❤️</div>
                                    <h4 style="margin-bottom: 8px; color: #333;">No liked posts</h4>
                                    <p>Posts you like will appear here</p>
                                </div>
                            </div>
                        </div>

                        <div class="tab-pane" id="activity-tab">
                            <div class="profile-activity" id="profileActivity">
                                <div style="text-align: center; padding: 40px 0; color: #666;">
                                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">📊</div>
                                    <h4 style="margin-bottom: 8px; color: #333;">No recent activity</h4>
                                    <p>Your recent interactions will appear here</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Loading State -->
                <div id="profileLoading" style="display: none; text-align: center; padding: 60px 0;">
                    <div class="loading-spinner" style="margin: 0 auto 20px;"></div>
                    <p>Loading profile...</p>
                </div>

                <!-- Profile Error State -->
                <div id="profileError" style="display: none; text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 48px; margin-bottom: 15px; opacity: 0.3;">⚠️</div>
                    <h4 style="margin-bottom: 8px; color: #333;">Error loading profile</h4>
                    <p>Please try refreshing the page</p>
                    <button class="refresh-btn" onclick="loadUserProfile()" style="margin-top: 15px;">Try Again</button>
                </div>
            </section>

            <!-- Settings Section -->
            <section class="content-section settings-section" id="settings-section">
                <div class="feed-header">
                    <h2 class="feed-title">Settings</h2>
                </div>
                <div style="text-align: center; padding: 60px 0; color: #666;">
                    <div style="font-size: 64px; margin-bottom: 20px; opacity: 0.3;">⚙️</div>
                    <h3 style="margin-bottom: 10px; color: #333;">Account Settings</h3>
                    <p>Manage your account preferences and privacy settings</p>
                </div>
            </section>

            <aside class="trending">
                <h3>Trending Topics</h3>
            </aside>
        </main>

        <!-- Mobile Navigation -->
        <nav class="mobile-nav">
            <div class="mobile-nav-items">
                <div class="mobile-nav-item active" data-section="feed">
                    <div class="icon">🏠</div>
                    <div class="label">Feed</div>
                </div>
                <div class="mobile-nav-item" data-section="explore">
                    <div class="icon">🔍</div>
                    <div class="label">Explore</div>
                </div>
                <div class="mobile-nav-item" data-section="messages">
                    <div class="icon">💬</div>
                    <div class="label">Messages</div>
                </div>
                <div class="mobile-nav-item" data-section="profile">
                    <div class="icon">👤</div>
                    <div class="label">Profile</div>
                </div>
                <div class="mobile-nav-item" data-section="settings">
                    <div class="icon">⚙️</div>
                    <div class="label">Settings</div>
                </div>
            </div>
        </nav>
    </div>

    <!-- Post Creation Modal -->
    <div id="postModal" class="post-modal" style="display: none;">
        <div class="post-modal-content">
            <div class="post-modal-header">
                <h3>Share Your Story</h3>
                <button id="closePostModal" class="close-btn">&times;</button>
            </div>
            <div class="post-modal-body">
                <form id="postForm">
                    <div class="form-group">
                        <label for="postTitle">Title</label>
                        <input type="text" id="postTitle" placeholder="Give your story a compelling title..." maxlength="100" required>
                        <div class="char-count" id="titleCharCount">0/100</div>
                    </div>

                    <div class="form-group">
                        <label for="postContent">Your Story</label>
                        <textarea id="postContent" placeholder="Share your positive experience, inspiration, or story that uplifts our community..." rows="8" maxlength="2000" required></textarea>
                        <div class="char-count" id="contentCharCount">0/2000</div>
                    </div>

                    <div class="form-group">
                        <label for="postCategory">Category</label>
                        <select id="postCategory" required>
                            <option value="">Select a category</option>
                            <option value="inspiration">Inspiration</option>
                            <option value="achievement">Achievement</option>
                            <option value="community">Community</option>
                            <option value="culture">Culture</option>
                            <option value="education">Education</option>
                            <option value="business">Business</option>
                            <option value="family">Family</option>
                            <option value="health">Health & Wellness</option>
                            <option value="arts">Arts & Creativity</option>
                            <option value="other">Other</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="postTags">Tags (optional)</label>
                        <input type="text" id="postTags" placeholder="Add tags separated by commas (e.g., motivation, success, community)">
                        <small>Help others discover your story with relevant tags</small>
                    </div>

                    <div class="form-group">
                        <label>Add Media (optional)</label>
                        <div class="media-upload-area" id="mediaUploadArea">
                            <div class="upload-placeholder">
                                <div class="upload-icon">📷</div>
                                <p>Click to add photos or drag and drop</p>
                                <small>Supported: JPG, PNG, GIF (max 5MB each)</small>
                            </div>
                            <input type="file" id="mediaInput" accept="image/*" multiple style="display: none;">
                        </div>
                        <div id="mediaPreview" class="media-preview"></div>
                    </div>

                    <div class="form-group">
                        <div class="post-options">
                            <label class="checkbox-label">
                                <input type="checkbox" id="allowComments" checked>
                                <span class="checkmark"></span>
                                Allow comments on this post
                            </label>
                            <label class="checkbox-label">
                                <input type="checkbox" id="shareToPublic" checked>
                                <span class="checkmark"></span>
                                Share with the public community
                            </label>
                        </div>
                    </div>

                    <div class="post-modal-footer">
                        <button type="button" id="saveDraftBtn" class="btn-secondary">Save Draft</button>
                        <button type="submit" id="publishPostBtn" class="btn-primary">Share Story</button>
                    </div>
                </form>
            </div>

            <div id="postLoading" class="post-loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Publishing your story...</p>
            </div>

            <div id="postError" class="post-error" style="display: none;">
                <div class="error-icon">⚠️</div>
                <p id="postErrorMessage">Something went wrong. Please try again.</p>
                <button id="retryPostBtn" class="btn-primary">Try Again</button>
            </div>
        </div>
    </div>

    <script type="module">
        // Import Firebase authentication
        import { FirebaseAuth } from './public/js/firebase-config.js';

        // Prevent redirect loops
        let redirectInProgress = false;

        // Add interactive functionality
        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize Firebase first
            let firebaseInitialized = false;
            try {
                firebaseInitialized = await FirebaseAuth.init();
                if (firebaseInitialized) {
                    console.log('Firebase initialized successfully');
                }
            } catch (error) {
                console.error('Error initializing Firebase:', error);
                firebaseInitialized = false;
            }

            // Check authentication status with fallback
            let isAuthenticated = false;
            let authCheckAttempted = false;

            // Always check localStorage first as fallback
            const currentUser = localStorage.getItem('currentUser');
            const authToken = localStorage.getItem('authToken');

            if (currentUser && authToken) {
                isAuthenticated = true;
                console.log('Found stored authentication data');

                // If Firebase is available, try to validate the session in background
                if (firebaseInitialized) {
                    try {
                        const sessionValidation = await FirebaseAuth.validateSession();
                        if (!sessionValidation.valid) {
                            console.warn('Firebase session validation failed, but keeping localStorage auth:', sessionValidation.reason);
                            // Don't immediately redirect, keep the localStorage auth as fallback
                        } else {
                            console.log('Firebase session validation successful');
                        }
                    } catch (error) {
                        console.warn('Firebase session validation error, using localStorage fallback:', error);
                    }
                }
            } else {
                console.log('No stored authentication found');
                isAuthenticated = false;
            }

            if (!isAuthenticated) {
                // No valid authentication found, redirect to landing page
                if (!redirectInProgress) {
                    redirectInProgress = true;
                    console.log('No valid authentication, redirecting to landing page');
                    window.location.href = '/landing.html';
                }
                return;
            }

            // Set up authentication state monitoring (but don't let it immediately redirect)
            let authStateChangeHandled = false;
            if (firebaseInitialized) {
                // Listen for auth state changes with delay to prevent immediate redirects
                setTimeout(() => {
                    FirebaseAuth.onAuthStateChanged((user) => {
                        if (!authStateChangeHandled) {
                            authStateChangeHandled = true;
                            return; // Ignore the first auth state change
                        }

                        if (!user) {
                            // User is not authenticated, redirect to landing page
                            console.log('Auth state changed: user signed out');
                            window.location.href = '/landing.html';
                        } else {
                            console.log('Auth state changed: user signed in', user.email);
                        }
                    });
                }, 1000); // Wait 1 second before setting up the listener
            }

            // Set up periodic session validation (less frequent and with better error handling)
            setInterval(async () => {
                if (firebaseInitialized) {
                    try {
                        const sessionValidation = await FirebaseAuth.validateSession();
                        if (!sessionValidation.valid) {
                            console.log('Session validation failed during periodic check:', sessionValidation.reason);
                            // Only redirect if we also don't have localStorage fallback
                            const fallbackUser = localStorage.getItem('currentUser');
                            if (!fallbackUser) {
                                window.location.href = '/landing.html';
                            } else {
                                console.log('Session validation failed but localStorage auth exists, staying logged in');
                            }
                        }
                    } catch (error) {
                        console.warn('Periodic session validation error:', error);
                        // Don't redirect on validation errors, just log them
                    }
                }
            }, 10 * 60 * 1000); // Check every 10 minutes (less frequent)
            // Navigation functionality with content switching
            function switchToSection(sectionName) {
                // Hide all content sections
                const allSections = document.querySelectorAll('.content-section');
                allSections.forEach(section => {
                    section.classList.remove('active');
                });

                // Show the selected section
                const targetSection = document.getElementById(`${sectionName}-section`);
                if (targetSection) {
                    targetSection.classList.add('active');
                }

                // Update sidebar navigation active state
                const sidebarItems = document.querySelectorAll('.sidebar-item');
                sidebarItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === sectionName) {
                        item.classList.add('active');
                    }
                });

                // Update mobile navigation active state
                const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
                mobileNavItems.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.section === sectionName) {
                        item.classList.add('active');
                    }
                });

                // Update URL hash for better UX (optional)
                window.location.hash = sectionName;

                console.log(`Switched to ${sectionName} section`);
            }

            // Sidebar navigation
            const sidebarItems = document.querySelectorAll('.sidebar-item');
            sidebarItems.forEach(item => {
                item.addEventListener('click', function() {
                    const sectionName = this.dataset.section;
                    if (sectionName) {
                        switchToSection(sectionName);
                    }
                });
            });

            // Mobile navigation
            const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
            mobileNavItems.forEach(item => {
                item.addEventListener('click', function() {
                    const sectionName = this.dataset.section;
                    if (sectionName) {
                        switchToSection(sectionName);
                    }
                });
            });

            // Handle URL hash on page load
            function handleInitialHash() {
                const hash = window.location.hash.substring(1); // Remove the #
                if (hash && document.getElementById(`${hash}-section`)) {
                    switchToSection(hash);
                } else {
                    // Default to feed section
                    switchToSection('feed');
                }
            }

            // Initialize with correct section based on URL hash
            handleInitialHash();

            // Handle browser back/forward navigation
            window.addEventListener('hashchange', handleInitialHash);

            // Refresh button functionality for all sections
            function setupRefreshButtons() {
                const refreshBtns = document.querySelectorAll('.refresh-btn');
                refreshBtns.forEach(btn => {
                    btn.addEventListener('click', function() {
                        // Visual feedback
                        this.style.transform = 'rotate(360deg)';
                        setTimeout(() => {
                            this.style.transform = 'rotate(0deg)';
                        }, 600);

                        // Get current active section
                        const activeSection = document.querySelector('.content-section.active');
                        if (activeSection) {
                            const sectionId = activeSection.id.replace('-section', '');
                            console.log(`Refreshing ${sectionId} content`);

                            // Here you can add specific refresh logic for each section
                            // For now, just show a message
                            // In the future, this could reload posts, messages, etc.
                        }
                    });
                });
            }

            // Initialize refresh buttons
            setupRefreshButtons();

            // Post creation functionality
            const postModal = document.getElementById('postModal');
            const closePostModal = document.getElementById('closePostModal');
            const postForm = document.getElementById('postForm');
            const createBtn = document.querySelector('.create-btn');

            // Character counting
            const titleInput = document.getElementById('postTitle');
            const contentTextarea = document.getElementById('postContent');
            const titleCharCount = document.getElementById('titleCharCount');
            const contentCharCount = document.getElementById('contentCharCount');

            // Media upload
            const mediaUploadArea = document.getElementById('mediaUploadArea');
            const mediaInput = document.getElementById('mediaInput');
            const mediaPreview = document.getElementById('mediaPreview');
            let selectedFiles = [];

            // Open post modal
            createBtn.addEventListener('click', function() {
                openPostModal();
            });

            // Close post modal
            closePostModal.addEventListener('click', function() {
                closePostModal();
            });

            // Close modal when clicking outside
            postModal.addEventListener('click', function(e) {
                if (e.target === postModal) {
                    closePostModalFunc();
                }
            });

            function openPostModal() {
                postModal.style.display = 'flex';
                document.body.style.overflow = 'hidden';
                titleInput.focus();
            }

            function closePostModalFunc() {
                if (confirm('Are you sure you want to close? Any unsaved changes will be lost.')) {
                    postModal.style.display = 'none';
                    document.body.style.overflow = 'auto';
                    resetPostForm();
                }
            }

            function resetPostForm() {
                postForm.reset();
                selectedFiles = [];
                mediaPreview.innerHTML = '';
                updateCharCount(titleInput, titleCharCount, 100);
                updateCharCount(contentTextarea, contentCharCount, 2000);
                hidePostError();
                hidePostLoading();
            }

            // Character counting
            function updateCharCount(input, countElement, maxLength) {
                const currentLength = input.value.length;
                countElement.textContent = `${currentLength}/${maxLength}`;

                countElement.classList.remove('warning', 'danger');
                if (currentLength > maxLength * 0.9) {
                    countElement.classList.add('danger');
                } else if (currentLength > maxLength * 0.8) {
                    countElement.classList.add('warning');
                }
            }

            titleInput.addEventListener('input', function() {
                updateCharCount(this, titleCharCount, 100);
            });

            contentTextarea.addEventListener('input', function() {
                updateCharCount(this, contentCharCount, 2000);
            });

            // Media upload functionality
            mediaUploadArea.addEventListener('click', function() {
                mediaInput.click();
            });

            mediaUploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                this.classList.add('dragover');
            });

            mediaUploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
            });

            mediaUploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('dragover');
                const files = Array.from(e.dataTransfer.files);
                handleFileSelection(files);
            });

            mediaInput.addEventListener('change', function(e) {
                const files = Array.from(e.target.files);
                handleFileSelection(files);
            });

            function handleFileSelection(files) {
                const validFiles = files.filter(file => {
                    if (!file.type.startsWith('image/')) {
                        alert(`${file.name} is not an image file.`);
                        return false;
                    }
                    if (file.size > 5 * 1024 * 1024) { // 5MB
                        alert(`${file.name} is too large. Maximum size is 5MB.`);
                        return false;
                    }
                    return true;
                });

                selectedFiles = [...selectedFiles, ...validFiles];
                if (selectedFiles.length > 5) {
                    alert('Maximum 5 images allowed.');
                    selectedFiles = selectedFiles.slice(0, 5);
                }

                updateMediaPreview();
            }

            function updateMediaPreview() {
                mediaPreview.innerHTML = '';
                selectedFiles.forEach((file, index) => {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const previewItem = document.createElement('div');
                        previewItem.className = 'media-preview-item';
                        previewItem.innerHTML = `
                            <img src="${e.target.result}" alt="Preview">
                            <button class="remove-media" onclick="removeMedia(${index})">&times;</button>
                        `;
                        mediaPreview.appendChild(previewItem);
                    };
                    reader.readAsDataURL(file);
                });
            }

            // Make removeMedia function global
            window.removeMedia = function(index) {
                selectedFiles.splice(index, 1);
                updateMediaPreview();
            };

            // Form submission
            postForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                await submitPost();
            });

            async function submitPost() {
                const title = titleInput.value.trim();
                const content = contentTextarea.value.trim();
                const category = document.getElementById('postCategory').value;
                const tags = document.getElementById('postTags').value.trim();
                const allowComments = document.getElementById('allowComments').checked;
                const shareToPublic = document.getElementById('shareToPublic').checked;

                // Validation
                if (!title || !content || !category) {
                    showPostError('Please fill in all required fields.');
                    return;
                }

                if (title.length > 100) {
                    showPostError('Title is too long (maximum 100 characters).');
                    return;
                }

                if (content.length > 2000) {
                    showPostError('Content is too long (maximum 2000 characters).');
                    return;
                }

                showPostLoading();

                try {
                    // Prepare post data
                    const postData = {
                        title: title,
                        content: content,
                        category: category,
                        tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
                        allowComments: allowComments,
                        shareToPublic: shareToPublic,
                        hasMedia: selectedFiles.length > 0
                    };

                    // For now, simulate API call
                    await new Promise(resolve => setTimeout(resolve, 2000));

                    console.log('Post data:', postData);
                    console.log('Selected files:', selectedFiles);

                    // TODO: Implement actual API call
                    alert('Post creation feature is being implemented! Your story will be published soon.');

                    hidePostLoading();
                    closePostModalFunc();
                } catch (error) {
                    console.error('Error creating post:', error);
                    hidePostLoading();
                    showPostError('Failed to publish your story. Please try again.');
                }
            }

            function showPostLoading() {
                document.querySelector('.post-modal-body').style.display = 'none';
                document.getElementById('postLoading').style.display = 'block';
                document.getElementById('postError').style.display = 'none';
            }

            function hidePostLoading() {
                document.querySelector('.post-modal-body').style.display = 'block';
                document.getElementById('postLoading').style.display = 'none';
            }

            function showPostError(message) {
                document.querySelector('.post-modal-body').style.display = 'none';
                document.getElementById('postLoading').style.display = 'none';
                document.getElementById('postError').style.display = 'block';
                document.getElementById('postErrorMessage').textContent = message;
            }

            function hidePostError() {
                document.getElementById('postError').style.display = 'none';
                document.querySelector('.post-modal-body').style.display = 'block';
            }

            // Retry button
            document.getElementById('retryPostBtn').addEventListener('click', function() {
                hidePostError();
            });

            // Save draft functionality
            document.getElementById('saveDraftBtn').addEventListener('click', function() {
                const draftData = {
                    title: titleInput.value,
                    content: contentTextarea.value,
                    category: document.getElementById('postCategory').value,
                    tags: document.getElementById('postTags').value,
                    allowComments: document.getElementById('allowComments').checked,
                    shareToPublic: document.getElementById('shareToPublic').checked,
                    timestamp: Date.now()
                };

                localStorage.setItem('postDraft', JSON.stringify(draftData));
                alert('Draft saved successfully!');
            });

            // Load draft on modal open
            function loadDraft() {
                const draft = localStorage.getItem('postDraft');
                if (draft) {
                    try {
                        const draftData = JSON.parse(draft);
                        if (confirm('You have a saved draft. Would you like to load it?')) {
                            titleInput.value = draftData.title || '';
                            contentTextarea.value = draftData.content || '';
                            document.getElementById('postCategory').value = draftData.category || '';
                            document.getElementById('postTags').value = draftData.tags || '';
                            document.getElementById('allowComments').checked = draftData.allowComments !== false;
                            document.getElementById('shareToPublic').checked = draftData.shareToPublic !== false;

                            updateCharCount(titleInput, titleCharCount, 100);
                            updateCharCount(contentTextarea, contentCharCount, 2000);
                        }
                    } catch (error) {
                        console.error('Error loading draft:', error);
                    }
                }
            }

            // Override openPostModal to include draft loading
            const originalOpenPostModal = openPostModal;
            openPostModal = function() {
                originalOpenPostModal();
                loadDraft();
            };

            // Post display system
            let currentPosts = [];
            let postsLoaded = 0;
            const postsPerPage = 5;

            // Sample posts data (will be replaced with API calls)
            const samplePosts = [
                {
                    id: '1',
                    title: 'Celebrating My Graduation from Medical School',
                    content: 'After years of hard work and dedication, I finally graduated from medical school! This journey taught me resilience, compassion, and the importance of community support. I\'m excited to serve my community as a healthcare provider and give back to those who believed in me.',
                    author: {
                        id: 'user1',
                        username: 'DrAmara',
                        displayName: 'Dr. Amara Johnson',
                        avatar: null
                    },
                    category: 'achievement',
                    tags: ['education', 'medical', 'graduation', 'success'],
                    createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
                    likes: 24,
                    comments: 8,
                    shares: 3,
                    media: [],
                    isLiked: false,
                    allowComments: true
                },
                {
                    id: '2',
                    title: 'Opening My First Restaurant',
                    content: 'Today marks the grand opening of "Mama\'s Kitchen" - a dream 10 years in the making! This restaurant celebrates our rich culinary heritage and provides jobs for 15 people in our community. Thank you to everyone who supported this journey. Come taste the love we put into every dish! 🍽️',
                    author: {
                        id: 'user2',
                        username: 'ChefMarcus',
                        displayName: 'Marcus Williams',
                        avatar: null
                    },
                    category: 'business',
                    tags: ['entrepreneur', 'restaurant', 'community', 'food'],
                    createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000), // 5 hours ago
                    likes: 67,
                    comments: 23,
                    shares: 12,
                    media: [],
                    isLiked: true,
                    allowComments: true
                },
                {
                    id: '3',
                    title: 'Community Garden Project Success',
                    content: 'Our neighborhood came together to transform an empty lot into a thriving community garden! Over 50 families now have access to fresh vegetables, and our children are learning about sustainable farming. This project shows what we can achieve when we work together for positive change.',
                    author: {
                        id: 'user3',
                        username: 'GreenThumb',
                        displayName: 'Keisha Thompson',
                        avatar: null
                    },
                    category: 'community',
                    tags: ['community', 'garden', 'sustainability', 'teamwork'],
                    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
                    likes: 45,
                    comments: 15,
                    shares: 8,
                    media: [],
                    isLiked: false,
                    allowComments: true
                },
                {
                    id: '4',
                    title: 'Scholarship Program Launch',
                    content: 'Proud to announce the launch of our scholarship program for underprivileged students! We\'re providing full tuition support for 20 students this year. Education is the key to breaking cycles and building stronger communities. Every child deserves the opportunity to reach their full potential.',
                    author: {
                        id: 'user4',
                        username: 'EduAdvocate',
                        displayName: 'Dr. James Carter',
                        avatar: null
                    },
                    category: 'education',
                    tags: ['education', 'scholarship', 'youth', 'opportunity'],
                    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
                    likes: 89,
                    comments: 31,
                    shares: 25,
                    media: [],
                    isLiked: true,
                    allowComments: true
                },
                {
                    id: '5',
                    title: 'Art Exhibition Showcasing Our Culture',
                    content: 'Just opened my first solo art exhibition "Roots and Wings" featuring 30 paintings that celebrate our heritage and envision our future. Art has the power to heal, inspire, and unite us. Thank you to everyone who came to the opening night - your support means everything!',
                    author: {
                        id: 'user5',
                        username: 'ArtistSoul',
                        displayName: 'Maya Robinson',
                        avatar: null
                    },
                    category: 'arts',
                    tags: ['art', 'culture', 'exhibition', 'heritage'],
                    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
                    likes: 52,
                    comments: 19,
                    shares: 7,
                    media: [],
                    isLiked: false,
                    allowComments: true
                }
            ];

            // Load posts function
            async function loadPosts(refresh = false) {
                const postsContainer = document.getElementById('postsContainer');
                const emptyState = document.getElementById('emptyState');
                const postsLoading = document.getElementById('postsLoading');
                const loadMoreBtn = document.getElementById('loadMoreBtn');

                if (refresh) {
                    postsLoaded = 0;
                    currentPosts = [];
                    postsContainer.innerHTML = '';
                }

                showPostsLoading();

                try {
                    // Simulate API call delay
                    await new Promise(resolve => setTimeout(resolve, 1000));

                    // Get posts for this page
                    const startIndex = postsLoaded;
                    const endIndex = startIndex + postsPerPage;
                    const newPosts = samplePosts.slice(startIndex, endIndex);

                    if (newPosts.length === 0 && currentPosts.length === 0) {
                        showEmptyState();
                        return;
                    }

                    currentPosts = [...currentPosts, ...newPosts];
                    postsLoaded += newPosts.length;

                    if (refresh) {
                        renderAllPosts();
                    } else {
                        renderNewPosts(newPosts);
                    }

                    hidePostsLoading();
                    updateLoadMoreButton();

                } catch (error) {
                    console.error('Error loading posts:', error);
                    hidePostsLoading();
                    // Show error state or keep existing posts
                }
            }

            function renderAllPosts() {
                const postsContainer = document.getElementById('postsContainer');
                postsContainer.innerHTML = '';
                currentPosts.forEach(post => {
                    const postElement = createPostElement(post);
                    postsContainer.appendChild(postElement);
                });
            }

            function renderNewPosts(posts) {
                const postsContainer = document.getElementById('postsContainer');
                posts.forEach(post => {
                    const postElement = createPostElement(post);
                    postsContainer.appendChild(postElement);
                });
            }

            function createPostElement(post) {
                const postDiv = document.createElement('div');
                postDiv.className = 'post-card';
                postDiv.dataset.postId = post.id;

                const timeAgo = getTimeAgo(post.createdAt);
                const authorInitial = post.author.displayName ? post.author.displayName[0].toUpperCase() : 'U';

                postDiv.innerHTML = `
                    <div class="post-header">
                        <div class="post-avatar">
                            ${post.author.avatar ? `<img src="${post.author.avatar}" alt="${post.author.displayName}">` : authorInitial}
                        </div>
                        <div class="post-user-info">
                            <div class="post-username">${post.author.displayName || post.author.username}</div>
                            <div class="post-meta">
                                <span class="post-category">${getCategoryDisplayName(post.category)}</span>
                                <span class="post-time">${timeAgo}</span>
                            </div>
                        </div>
                        <div class="post-menu">
                            <button class="post-menu-btn" onclick="showPostMenu('${post.id}')">⋯</button>
                        </div>
                    </div>

                    <div class="post-content">
                        <h3 class="post-title">${post.title}</h3>
                        <p class="post-text">${post.content}</p>
                        ${post.media && post.media.length > 0 ? renderPostMedia(post.media) : ''}
                        ${post.tags && post.tags.length > 0 ? renderPostTags(post.tags) : ''}
                    </div>

                    <div class="post-actions">
                        <div class="post-actions-left">
                            <button class="post-action ${post.isLiked ? 'liked' : ''}" onclick="toggleLike('${post.id}')">
                                <span class="post-action-icon">${post.isLiked ? '❤️' : '🤍'}</span>
                                <span>${post.likes}</span>
                            </button>
                            <button class="post-action" onclick="showComments('${post.id}')">
                                <span class="post-action-icon">💬</span>
                                <span>${post.comments}</span>
                            </button>
                            <button class="post-action" onclick="sharePost('${post.id}')">
                                <span class="post-action-icon">📤</span>
                                <span>${post.shares}</span>
                            </button>
                        </div>
                        <div class="post-stats">
                            <span>${post.likes + post.comments + post.shares} interactions</span>
                        </div>
                    </div>
                `;

                return postDiv;
            }

            function renderPostMedia(media) {
                if (!media || media.length === 0) return '';

                if (media.length === 1) {
                    return `<div class="post-media"><img src="${media[0]}" alt="Post media"></div>`;
                }

                const gridClass = media.length === 2 ? 'grid-2' :
                                 media.length === 3 ? 'grid-3' : 'grid-4';

                const mediaHtml = media.slice(0, 4).map(src => `<img src="${src}" alt="Post media">`).join('');

                return `<div class="post-media"><div class="post-media-grid ${gridClass}">${mediaHtml}</div></div>`;
            }

            function renderPostTags(tags) {
                if (!tags || tags.length === 0) return '';

                const tagsHtml = tags.map(tag => `<span class="post-tag">#${tag}</span>`).join('');
                return `<div class="post-tags">${tagsHtml}</div>`;
            }

            function getCategoryDisplayName(category) {
                const categories = {
                    'inspiration': 'Inspiration',
                    'achievement': 'Achievement',
                    'community': 'Community',
                    'culture': 'Culture',
                    'education': 'Education',
                    'business': 'Business',
                    'family': 'Family',
                    'health': 'Health & Wellness',
                    'arts': 'Arts & Creativity',
                    'other': 'Other'
                };
                return categories[category] || 'Other';
            }

            function getTimeAgo(date) {
                const now = new Date();
                const diffInSeconds = Math.floor((now - date) / 1000);

                if (diffInSeconds < 60) return 'Just now';
                if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
                if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
                if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;

                return date.toLocaleDateString();
            }

            function showPostsLoading() {
                document.getElementById('postsLoading').style.display = 'block';
                document.getElementById('emptyState').style.display = 'none';
            }

            function hidePostsLoading() {
                document.getElementById('postsLoading').style.display = 'none';
            }

            function showEmptyState() {
                document.getElementById('emptyState').style.display = 'block';
                document.getElementById('postsLoading').style.display = 'none';
                document.getElementById('loadMoreBtn').style.display = 'none';
            }

            function updateLoadMoreButton() {
                const loadMoreBtn = document.getElementById('loadMoreBtn');
                if (postsLoaded < samplePosts.length) {
                    loadMoreBtn.style.display = 'block';
                } else {
                    loadMoreBtn.style.display = 'none';
                }
            }

            // Post interaction functions (global scope for onclick handlers)
            window.toggleLike = function(postId) {
                const post = currentPosts.find(p => p.id === postId);
                if (post) {
                    post.isLiked = !post.isLiked;
                    post.likes += post.isLiked ? 1 : -1;

                    // Update the UI
                    const postElement = document.querySelector(`[data-post-id="${postId}"]`);
                    const likeButton = postElement.querySelector('.post-action');
                    const likeIcon = likeButton.querySelector('.post-action-icon');
                    const likeCount = likeButton.querySelector('span:last-child');

                    likeButton.classList.toggle('liked', post.isLiked);
                    likeIcon.textContent = post.isLiked ? '❤️' : '🤍';
                    likeCount.textContent = post.likes;

                    // Update stats
                    const stats = postElement.querySelector('.post-stats span');
                    const totalInteractions = post.likes + post.comments + post.shares;
                    stats.textContent = `${totalInteractions} interactions`;

                    console.log(`${post.isLiked ? 'Liked' : 'Unliked'} post: ${postId}`);
                }
            };

            window.showComments = function(postId) {
                console.log(`Show comments for post: ${postId}`);
                alert('Comments feature coming soon!');
            };

            window.sharePost = function(postId) {
                console.log(`Share post: ${postId}`);
                alert('Share feature coming soon!');
            };

            window.showPostMenu = function(postId) {
                console.log(`Show menu for post: ${postId}`);
                alert('Post menu coming soon!');
            };

            // Load more button
            document.getElementById('loadMoreBtn').addEventListener('click', function() {
                this.textContent = 'Loading...';
                loadPosts().then(() => {
                    this.textContent = 'Load More Stories';
                });
            });

            // Refresh button functionality for feed
            const feedRefreshBtn = document.querySelector('.feed-section .refresh-btn');
            feedRefreshBtn.addEventListener('click', function() {
                // Visual feedback
                this.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    this.style.transform = 'rotate(0deg)';
                }, 600);

                loadPosts(true);
            });

            // Load initial posts when switching to feed
            const originalSwitchToSection = switchToSection;
            switchToSection = function(sectionName) {
                originalSwitchToSection(sectionName);

                if (sectionName === 'feed' && currentPosts.length === 0) {
                    loadPosts(true);
                }
            };

            // Load posts on page load if we're on the feed section
            if (document.querySelector('.feed-section').classList.contains('active')) {
                loadPosts(true);
            }

            // Logout button functionality
            const logoutBtn = document.querySelector('.nav-btn.primary');
            logoutBtn.addEventListener('click', async function() {
                if (confirm('Do you want to logout?')) {
                    try {
                        // Show loading state
                        this.textContent = 'Signing out...';
                        this.disabled = true;

                        if (firebaseInitialized) {
                            // Use Firebase session management
                            const result = await FirebaseAuth.signOut();
                            if (result.success) {
                                console.log('Successfully signed out');
                            } else {
                                console.warn('Sign out warning:', result.error);
                            }
                        } else {
                            // Fallback: clear local storage manually
                            localStorage.removeItem('currentUser');
                            localStorage.removeItem('authToken');
                            localStorage.removeItem('sessionData');
                            sessionStorage.clear();
                        }

                        // Redirect to landing page
                        window.location.href = '/landing.html';
                    } catch (error) {
                        console.error('Error during logout:', error);

                        // Force clear session and redirect anyway
                        localStorage.clear();
                        sessionStorage.clear();
                        window.location.href = '/landing.html';
                    }
                }
            });

            // Profile functionality
            let profileUser = null;

            // Load user profile
            async function loadUserProfile() {
                try {
                    showProfileLoading();

                    // Get current user from localStorage
                    const storedUser = localStorage.getItem('currentUser');
                    if (storedUser) {
                        profileUser = JSON.parse(storedUser);
                    }

                    // Try to get updated profile from server
                    const authToken = localStorage.getItem('authToken');
                    if (authToken) {
                        try {
                            const response = await fetch('/api/user/profile', {
                                headers: {
                                    'Authorization': `Bearer ${authToken}`
                                }
                            });

                            if (response.ok) {
                                const data = await response.json();
                                if (data.success && data.user) {
                                    profileUser = data.user;
                                    localStorage.setItem('currentUser', JSON.stringify(profileUser));
                                }
                            }
                        } catch (error) {
                            console.warn('Could not fetch updated profile from server:', error);
                        }
                    }

                    if (profileUser) {
                        displayUserProfile(profileUser);
                        hideProfileLoading();
                    } else {
                        showProfileError();
                    }
                } catch (error) {
                    console.error('Error loading user profile:', error);
                    showProfileError();
                }
            }

            // Display user profile
            function displayUserProfile(user) {
                // Update profile information
                document.getElementById('profileDisplayName').textContent = user.username || user.displayName || 'User';
                document.getElementById('profileUsername').textContent = `@${user.username || user.email?.split('@')[0] || 'user'}`;
                document.getElementById('profileEmail').textContent = user.email || 'No email';
                document.getElementById('profileBio').textContent = user.bio || 'No bio available';

                // Update stats
                document.getElementById('profileStories').textContent = user.stories || 0;
                document.getElementById('profileFollowers').textContent = user.followers || 0;
                document.getElementById('profileFollowing').textContent = user.following || 0;

                // Update avatar
                const avatarImg = document.getElementById('profileAvatarImg');
                const avatarPlaceholder = document.getElementById('avatarPlaceholder');

                if (user.avatar) {
                    avatarImg.src = user.avatar;
                    avatarImg.style.display = 'block';
                    avatarPlaceholder.style.display = 'none';
                } else {
                    avatarImg.style.display = 'none';
                    avatarPlaceholder.style.display = 'flex';
                    avatarPlaceholder.textContent = (user.username || user.email || 'U')[0].toUpperCase();
                }
            }

            // Show/hide profile states
            function showProfileLoading() {
                document.getElementById('profileContent').style.display = 'none';
                document.getElementById('profileLoading').style.display = 'block';
                document.getElementById('profileError').style.display = 'none';
            }

            function hideProfileLoading() {
                document.getElementById('profileContent').style.display = 'block';
                document.getElementById('profileLoading').style.display = 'none';
                document.getElementById('profileError').style.display = 'none';
            }

            function showProfileError() {
                document.getElementById('profileContent').style.display = 'none';
                document.getElementById('profileLoading').style.display = 'none';
                document.getElementById('profileError').style.display = 'block';
            }

            // Profile tab switching
            const profileTabs = document.querySelectorAll('.profile-tab');
            const tabPanes = document.querySelectorAll('.tab-pane');

            profileTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const targetTab = this.dataset.tab;

                    // Update active tab
                    profileTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');

                    // Update active pane
                    tabPanes.forEach(pane => pane.classList.remove('active'));
                    document.getElementById(`${targetTab}-tab`).classList.add('active');

                    console.log(`Switched to ${targetTab} tab`);
                });
            });

            // Profile refresh button
            document.getElementById('profileRefreshBtn').addEventListener('click', function() {
                // Visual feedback
                this.style.transform = 'rotate(360deg)';
                setTimeout(() => {
                    this.style.transform = 'rotate(0deg)';
                }, 600);

                loadUserProfile();
            });

            // Edit profile button
            document.getElementById('editProfileBtn').addEventListener('click', function() {
                alert('Profile editing feature coming soon!');
            });

            // Change avatar button
            document.getElementById('changeAvatarBtn').addEventListener('click', function() {
                document.getElementById('avatarInput').click();
            });

            // Avatar file input
            document.getElementById('avatarInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    // For now, just show an alert. In the future, implement file upload
                    alert('Avatar upload feature coming soon!');
                    console.log('Selected file:', file.name);
                }
            });

            // Load profile when switching to profile section
            const originalSwitchToSection = switchToSection;
            switchToSection = function(sectionName) {
                originalSwitchToSection(sectionName);

                if (sectionName === 'profile') {
                    loadUserProfile();
                }
            };

            // Trending topics
            const trendingItems = document.querySelectorAll('.trending-item');
            trendingItems.forEach(item => {
                item.addEventListener('click', function() {
                    const topic = this.querySelector('.trending-topic').textContent;
                    alert(`Exploring ${topic}...`);
                });
            });
        });
    </script>
</body>
</html>